import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgr from 'vite-plugin-svgr';
import tsconfigPaths from 'vite-tsconfig-paths';
import { VitePWA } from 'vite-plugin-pwa';
import path from 'path';

const APPS = ['shop', 'gpo-portal'];

export default defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };
  const app = process.env.APP || APPS[0];

  console.log(`Selected app: ${app}`);

  return {
    server: {
      port: 3000, // + APPS.indexOf(app),
      host: '0.0.0.0',
      open: true,
    },
    plugins: [
      react(),
      svgr(),
      tsconfigPaths(),
      VitePWA({
        registerType: 'autoUpdate',
        includeAssets: ['favicon.svg', 'favicon.ico', 'apple-touch-icon.png'],
        manifest: {
          name: 'HighFive',
          short_name: 'HighFive',
          start_url: '/',
          display: 'standalone',
          background_color: '#FFFFFF',
          theme_color: '#FFFFFF',
          icons: [
            {
              src: 'web-app-manifest-192x192.png',
              sizes: '192x192',
              type: 'image/png',
              purpose: 'maskable',
            },
            {
              src: 'web-app-manifest-512x512.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'maskable',
            },
          ],
        },
        workbox: {
          cleanupOutdatedCaches: true,
          navigateFallback: 'index.html',
          globPatterns: ['**/*.{js,css,html,ico,png,svg}'],
          runtimeCaching: [
            {
              urlPattern: ({ request }) => request.destination === 'script',
              handler: 'CacheFirst',
              options: {
                cacheName: 'lazy-scripts',
                expiration: {
                  maxEntries: 100,
                  maxAgeSeconds: 60 * 60 * 24 * 5, // 5 day
                },
              },
            },
          ],
        },
      }),
    ],
    test: {
      environment: 'jsdom',
      globals: true,
      setupFiles: path.resolve(__dirname, './vitest.setup.js'),
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    root: path.resolve(__dirname, `src/apps/${app}`),
    base: './',
    publicDir: path.resolve(__dirname, './public'),
    build: {
      outDir: path.resolve(__dirname, `build/${app}`),
      sourcemap: process.env.NODE_ENV === 'staging',
      emptyOutDir: true,
    },
  };
});
