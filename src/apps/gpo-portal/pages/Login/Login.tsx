import { useNavigate } from 'react-router-dom';

import { GPO_ROUTES_PATH } from '@/apps/gpo-portal/routes/routes';
import {
  type FormValues,
  LoginForm,
} from '@/libs/auth/components/LoginForm/LoginForm';
import { post } from '@/libs/utils/api';
import { UserType } from '@/types/common';

import { SCHEMA } from './constants';

export const Login = () => {
  const navigate = useNavigate();

  const loginApiFunc = async (values?: FormValues) => {
    if (!values) return;

    const { email, password } = values;

    const userData = await post<UserType>({
      url: '/gpo/sessions',
      body: { email, password },
      withApi: false,
    });

    return userData;
  };

  return (
    <LoginForm
      apiFunc={loginApiFunc}
      onSuccess={() => navigate(GPO_ROUTES_PATH.dashboard)}
      forgotPasswordPath={GPO_ROUTES_PATH.forgotPassword}
      schema={SCHEMA}
      namespace="gpo.login"
    />
  );
};
