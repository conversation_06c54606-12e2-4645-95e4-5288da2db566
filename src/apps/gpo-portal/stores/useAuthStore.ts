import { create } from 'zustand';
import { UserType } from '@/types/common';
import { deleteApi } from '@/libs/utils/api';

interface AuthState {
  user: UserType | null;
  setUser: (user: UserType | null) => void;
  logout: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  setUser: (user) => set({ user }),
  logout: async () => {
    await deleteApi({
      url: '/gpo/sessions',
      withApi: false,
    });
    set({ user: null });
  },
}));
