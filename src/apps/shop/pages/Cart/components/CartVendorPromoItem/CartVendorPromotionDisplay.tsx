import { Badge } from '@/libs/ui/Badge/Badge';
import { OfferType } from '@/types';
import { getPriceString } from '@/utils';
import { CartItemType } from '@/libs/cart/types';
import { AddToCartInput } from '@/libs/products/components/AddToCartInput/AddToCartInput';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { getPromotionDetails } from '@/libs/promotions/utils/promotionUtils';

type CartVendorPromotionDisplayProps = {
  item: CartItemType & {
    freeItemsQty: number;
    freeOffer: OfferType | null;
  };
};

type QuantityUpdate = {
  amount: number;
  setError: (message: string) => void;
};

export const CartVendorPromotionDisplay = ({
  item,
}: CartVendorPromotionDisplayProps) => {
  const { addToCart } = useCartStore();

  const offer = item.product.offers.find((o) => o.id === item.productOfferId);

  const promotionDetails = getPromotionDetails({ item });
  const minimumQuantity = promotionDetails?.minimumQuantity || 1;
  const minAllowedForThisItem = Math.max(1, minimumQuantity - item.quantity);

  const handleQuantityUpdate = ({ amount, setError }: QuantityUpdate) => {
    if (amount < minAllowedForThisItem) {
      setError(
        `Minimum ${minAllowedForThisItem} required to maintain promotion`,
      );
      return;
    }

    addToCart({
      offers: [
        {
          productOfferId: item.productOfferId,
          quantity: amount,
        },
      ],
      onError: (message) => {
        setError(message);
      },
    });
  };

  return (
    <div className="grid w-full gap-1 py-3">
      {/* Paid item row */}
      <div className="flex items-center gap-2">
        <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
          {item.quantity}
        </Badge>
        <span className="flex-1 text-xs font-medium">{item.product.name}</span>
        <div className="flex items-center gap-2">
          <span className="min-w-[80px] text-right text-xs font-medium text-neutral-500">
            {getPriceString(item.subtotal)}
          </span>
          <div className="max-w-20">
            <AddToCartInput
              originalAmount={item.quantity}
              minIncrement={offer?.increments || 1}
              onUpdate={handleQuantityUpdate}
              size="sm"
            />
          </div>
        </div>
      </div>

      {/* Free item row */}
      {item.freeItemsQty > 0 && item.freeOffer && (
        <div className="flex items-center gap-2">
          <Badge className="text-sxs h-5 w-7 rounded-sm bg-[#B6F5F959] px-0 py-0">
            {item.freeItemsQty}
          </Badge>
          <span className="flex-1 text-xs font-medium">
            {item.freeOffer.name}
          </span>
          <span className="min-w-[80px] text-right text-xs font-medium text-green-700/90">
            Free
          </span>
        </div>
      )}
    </div>
  );
};
