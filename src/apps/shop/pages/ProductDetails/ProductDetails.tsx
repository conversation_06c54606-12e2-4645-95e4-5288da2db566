import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Location, useParams, useSearchParams } from 'react-router-dom';
import {
  Text,
  Image,
  LoadingOverlay,
  Rating,
  Title,
  List,
  Divider,
} from '@mantine/core';
import dayjs from 'dayjs';

import { ErrorSection } from '@/components';
import { PageHeader } from '@/apps/shop/components/PageHeader/PageHeader';
import {
  DEFAULT_DISPLAY_DATE_FORMAT,
  FEATURE_FLAGS,
  PROMO_TYPE,
} from '@/constants';
import ClockIcon from '@/assets/images/product/clock.svg?react';
import defaultProductImgUrl from '@/assets/images/default-product.png';

import { useProductStore } from '@/apps/shop/stores/useProductStore/useProductStore';
import { FavoriteButton } from '@/libs/products/components/FavoriteButton/FavoriteButton';
import { GpoRecommendedTag } from '@/libs/gpo/components/GpoRecommendedTag/GpoRecommendedTag';
import { getPriceString } from '@/utils';
import { PurchaseHistoryChart } from '@/libs/products/components/PurchaseHistory/PurchaseHistoryChart';
import { ProductDetailPanel } from '@/libs/products/components/ProductDetailPanel/ProductDetailPanel';
import { SavingAlert } from '@/libs/gpo/components/SavingAlert/SavingAlert';
import { ProductReviewsList } from '@/libs/products/components/ProductReviewsList/ProductReviewsList';
import { ProductSuggestedList } from '@/libs/products/components/ProductSuggestedList/ProductSuggestedList';
import { SpecialInstructionIconList } from '@/libs/products/components/SpecialInstructionIconList/SpecialInstructionIconList';
import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { useNavigationHistory } from '@/libs/utils/navigation/hooks/useNavigationHistory';
import { BreadCrumbs } from '@/libs/ui/BreadCrumbs/BreadCrumbs';
import { RebatePanel } from './components/RebatePanel/RebatePanel';
import { MarkdownRenderer } from '@/libs/ui/MarkdownRenderer/MarkdownRenderer';
import styles from './ProductDetails.module.css';
import { AddToCartForm } from '@/libs/products/components/AddToCartForm/AddToCartForm';
import { getProductOfferComputedData } from '@/libs/products/utils/getProductComputedData';
import { NetPriceRebateIcon } from '@/libs/products/components/NetPriceRebateIcon/NetPriceRebateIcon';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useQuery } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';
import { Dollar } from '@/libs/products/components/Dollar/Dollar';

const getFirstBreadcrumbItem = (navigationHistory: Location[]) => {
  const previousNavigation =
    navigationHistory[navigationHistory.length - 1] || {};

  const allowedPathNames = ['cart', 'search', 'order-history'];
  const path = allowedPathNames.includes(
    previousNavigation.pathname?.split('/')[1],
  )
    ? previousNavigation.pathname
    : '/cart';
  return {
    path,
    name: '',
    search: previousNavigation.search,
  };
};

export const ProductDetails = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();

  const [selectedOfferId, setSelectedOfferId] = useState<string | null>(
    searchParams.get('selectedOfferId') ?? null,
  );
  const params = useParams();
  const navigationHistory = useNavigationHistory();

  const { fetchProductDetails } = useProductStore();

  const {
    data: productDetails,
    isError,
    isLoading,
  } = useQuery({
    queryKey: queryKeys.products.details(params.id),
    queryFn: () => fetchProductDetails(params.id),
  });

  const pageHeader = <PageHeader title={t('client.productItem.title')} />;

  if (isError) {
    return (
      <div className="mainSection">
        {pageHeader}
        <ErrorSection title={t(`apiErrors.general`)} />
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="mainSection">
        {pageHeader}
        <div className="relative">
          <LoadingOverlay visible />
        </div>
      </div>
    );
  }

  if (!productDetails) {
    return null;
  }

  const {
    id,
    isFavorite,
    imageUrl,
    name,
    description,
    attributes,
    manufacturer,
    manufacturerSku,
    offers,
    promotions,
  } = productDetails;

  const productOffer =
    productDetails.offers.find(({ id }) => id === selectedOfferId) ??
    productDetails.offers[0];

  const {
    id: productOfferId,
    lastOrderedQuantity,
    lastOrderedAt,
    vendorSku,
    isRecommended,
    increments,
  } = productOffer;

  const { hasRebate, originalPrice, salePrice, gpoSavings } =
    getProductOfferComputedData(productOffer);

  const handleSelectSwapVendor = (newProductOfferId: string) => {
    setSelectedOfferId(newProductOfferId);
  };

  const breadcrumbsItems = [
    getFirstBreadcrumbItem(navigationHistory),
    {
      path: '',
      name,
      search: '',
    },
  ];

  return (
    <div className="mainSection">
      {pageHeader}
      <div className="relative mx-auto max-w-[825px]">
        <div className="mb-2">
          <BreadCrumbs items={breadcrumbsItems} />
        </div>
        <div className={styles.innerContent}>
          <LoadingOverlay visible={isLoading} />
          <div className={styles.image}>
            <div
              className="absolute top-4 right-4"
              style={{ transform: 'scale(1.5)' }}
            >
              <FavoriteButton productId={id} isFavorite={isFavorite} />
            </div>
            <GpoRecommendedTag
              isRecommended={isRecommended}
              size="lg"
              top="1rem"
            />

            <Image
              src={imageUrl}
              alt={name}
              fallbackSrc={defaultProductImgUrl}
            />
            <div
              className="absolute right-10 bottom-8"
              style={{ transform: 'scale(1.3)' }}
            >
              <SpecialInstructionIconList {...productDetails} />
            </div>
          </div>

          <div className={styles.productInfo}>
            <div className={styles.details}>
              <div className="mb-4">
                <Title size="h2" order={1}>
                  {name}
                </Title>

                {FEATURE_FLAGS.PRODUCT_REVIEWS && (
                  <Flex align="center" gap="8" mt="0.2rem">
                    <>
                      <Rating defaultValue={4} />
                      <Text fw="700">20 REVIEWS</Text>
                    </>
                  </Flex>
                )}
              </div>

              {lastOrderedAt && (
                <Flex align="center" mt="0.75rem">
                  <ClockIcon />
                  <Text size="0.75rem" ml="4">
                    {lastOrderedQuantity} on{' '}
                    {dayjs(lastOrderedAt).format(DEFAULT_DISPLAY_DATE_FORMAT)}
                  </Text>
                </Flex>
              )}

              <Flex mt="md" mb="1.5rem">
                <VendorSwap
                  offers={offers}
                  currentOfferId={productOfferId}
                  onSwap={handleSelectSwapVendor}
                />
              </Flex>

              <List className={styles.productDetailList}>
                <List.Item>
                  <Text span className={styles.productDetailListTitle}>
                    {t('client.productItem.sku')}:
                  </Text>{' '}
                  {vendorSku}
                </List.Item>
                {manufacturer && (
                  <List.Item>
                    <Text span className={styles.productDetailListTitle}>
                      MFR:
                    </Text>{' '}
                    {manufacturer}
                  </List.Item>
                )}
                {manufacturerSku && (
                  <List.Item>
                    <Text span className={styles.productDetailListTitle}>
                      MFR SKU:
                    </Text>{' '}
                    {manufacturerSku}
                  </List.Item>
                )}
              </List>
            </div>
            <Flex direction="column" mt="1.5rem">
              <Flex align="center" mb="0.75rem" gap="0.5rem">
                {originalPrice > salePrice ? (
                  <Flex align="flex-end" gap="10">
                    <Text size="2rem" fw="500">
                      {getPriceString(salePrice)}
                    </Text>

                    <Text
                      size="1rem"
                      fw="500"
                      td="line-through"
                      c="rgba(51, 51, 51, 0.50)"
                    >
                      {getPriceString(originalPrice)}
                    </Text>
                  </Flex>
                ) : (
                  <Text size="2rem" fw="500">
                    {getPriceString(salePrice)}
                  </Text>
                )}
                {hasRebate && <NetPriceRebateIcon />}
                {promotions?.length ? (
                  <Dollar
                    toolTipLabel={`Promotion Type: ${PROMO_TYPE[promotions[0].type]}`}
                  />
                ) : null}
              </Flex>
              <div className="max-w-full">
                <AddToCartForm
                  productOfferId={productOfferId}
                  increments={increments}
                />
              </div>
              {gpoSavings ? (
                <div className="mt-4">
                  <SavingAlert value={gpoSavings} />
                </div>
              ) : null}
            </Flex>
          </div>
        </div>

        {description || attributes?.length ? (
          <Flex direction="column">
            {description && (
              <div className="mb-6">
                <Divider mb="1.5rem" />
                <Title order={3} size="md" mb="1rem">
                  Description
                </Title>
                <MarkdownRenderer markdown={description} />
              </div>
            )}
            {attributes?.length ? (
              <div className="mb-6">
                <Divider mb="1.5rem" />
                <Title order={3} size="md" mb="1rem">
                  Attributes/Specifications
                </Title>
                <List className={styles.productDetailList}>
                  {attributes.map(({ name, value }) => (
                    <List.Item key={name}>
                      <Text span c="#666">
                        <Text span fw="500" c="#333">
                          {name}:
                        </Text>
                        {' ' + value}
                      </Text>
                    </List.Item>
                  ))}
                </List>
              </div>
            ) : null}
          </Flex>
        ) : null}

        <Divider mb="2rem" />

        <RebatePanel productOfferId={productOfferId} />

        <Flex mb="2rem" direction="column">
          <CollapsiblePanel
            header={
              <Flex h="100%" align="center" ml="1.5rem">
                <Text size="md" fw={500}>
                  Purchase History
                </Text>
              </Flex>
            }
            content={
              <div className="p-4">
                <PurchaseHistoryChart productId={productOfferId} />
              </div>
            }
            startOpen
          />
        </Flex>

        <Flex gap="2rem" direction="column">
          {FEATURE_FLAGS.SUBSTITUTES ? (
            <ProductDetailPanel title="Substitutes">
              <ProductSuggestedList products={[]} />
            </ProductDetailPanel>
          ) : null}
          {FEATURE_FLAGS.PRODUCT_REVIEWS && (
            <ProductDetailPanel title="Reviews (20)">
              <div className="pt-4">
                <ProductReviewsList reviews={[]} />
              </div>
            </ProductDetailPanel>
          )}
        </Flex>
      </div>
    </div>
  );
};

export const ProductDetailsWrapper = () => {
  const params = useParams();

  return <ProductDetails key={params.id} />;
};
