import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Icon } from '@/libs/icons/Icon';
import clsx from 'clsx';

const percentageDisplayVariants = cva('flex items-center gap-1', {
  variants: {
    size: {
      sm: '',
      md: '',
      lg: '',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

const iconContainerVariants = cva(
  'flex items-center justify-center rounded-full',
  {
    variants: {
      size: {
        sm: 'h-2 w-2',
        md: 'h-3 w-3',
        lg: 'h-4 w-4',
      },
      variant: {
        positive: 'bg-[#6AA555]',
        negative: 'bg-[#C74859]',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'positive',
    },
  },
);

const textVariants = cva('flex items-center font-medium', {
  variants: {
    size: {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
    },
    variant: {
      positive: 'text-[#6AA555]',
      negative: 'text-[#C74859]',
    },
  },
  defaultVariants: {
    size: 'md',
    variant: 'positive',
  },
});

const labelVariants = cva('ml-1 text-[#98A2B3]', {
  variants: {
    size: {
      sm: 'text-[8px]',
      md: 'text-sxs',
      lg: 'text-xs',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

const iconSizeMap = {
  sm: '4px',
  md: '8px',
  lg: '12px',
} as const;

interface PercentageDisplayProps
  extends VariantProps<typeof percentageDisplayVariants> {
  percentage: number;
  label?: string;
  showIcon?: boolean;
  className?: string;
}

export const PercentageDisplay = ({
  percentage,
  label = '',
  size = 'md',
  showIcon = true,
  className = '',
}: PercentageDisplayProps) => {
  const isPositive = percentage >= 0;
  const variant = isPositive ? 'positive' : 'negative';
  const currentSize = size || 'md';

  return (
    <div className={clsx(percentageDisplayVariants({ size }), className)}>
      {showIcon && (
        <div className={iconContainerVariants({ size, variant })}>
          <Icon
            name="arrowUp"
            color="white"
            size={iconSizeMap[currentSize]}
            className={clsx(isPositive ? '' : 'rotate-180')}
            aria-label={isPositive ? 'increase' : 'decrease'}
          />
        </div>
      )}
      <span className={textVariants({ size, variant })}>
        {Math.abs(percentage)}%
        {label && <span className={labelVariants({ size })}>{label}</span>}
      </span>
    </div>
  );
};
