import { Input, InputProps } from '@/libs/form/Input';
import { Group, Popover } from '@mantine/core';
import { Button } from '@/libs/ui/Button/Button';
import {
  type KeyboardEventHandler,
  useState,
  useRef,
  useEffect,
  useCallback,
} from 'react';
import styles from './AddToCartInput.module.css';
import MinusIcon from './assets/minus.svg?react';
import PlusIcon from './assets/plus.svg?react';

export interface AddToCartInputProps {
  hideButtons?: boolean;
  originalAmount: number;
  minIncrement: number;
  size?: InputProps['size'];
  onUpdate: ({
    amount,
    setError,
  }: {
    amount: number;
    setError: (message: string) => void;
  }) => void;
}
export const AddToCartInput = ({
  hideButtons = false,
  originalAmount,
  minIncrement,
  onUpdate,
  size = 'md',
}: AddToCartInputProps) => {
  const [error, setError] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);
  const [showIncrementInfo, setShowIncrementInfo] = useState(false);
  const originalMinIncrementRef = useRef(minIncrement);

  const handleUpdateValue = useCallback(() => {
    if (!inputRef.current?.value) {
      return;
    }

    setShowIncrementInfo(false);
    let newValue = +inputRef.current.value;

    if (newValue <= 0) {
      inputRef.current.value = originalAmount.toString();
      return;
    }

    if (newValue % minIncrement !== 0) {
      newValue = (Math.floor(newValue / minIncrement) || 1) * minIncrement;
      inputRef.current.value = newValue.toString();
    }

    onUpdate({ amount: newValue, setError });
  }, [inputRef, originalAmount, minIncrement, onUpdate]);

  useEffect(() => {
    if (inputRef.current && originalMinIncrementRef.current !== minIncrement) {
      inputRef.current.value = minIncrement.toString();
      originalMinIncrementRef.current = minIncrement;
      onUpdate({ amount: minIncrement, setError });
    }
  }, [minIncrement, onUpdate]);

  const handleChange: KeyboardEventHandler<HTMLInputElement> = (event) => {
    if (event.code === 'Enter') {
      handleUpdateValue();
    }
  };

  const handleControlClick = (increment: number) => () => {
    if (!inputRef.current) {
      return;
    }

    inputRef.current.value = (+inputRef.current.value + increment).toString();
    handleUpdateValue();
  };

  return (
    <Popover opened={showIncrementInfo}>
      <Popover.Target>
        <Group pos="relative" align="center">
          {!hideButtons && (
            <Button
              type="button"
              variant="unstyled"
              className={`${styles.control} ${styles.minus}`}
              onClick={handleControlClick(minIncrement * -1)}
            >
              <MinusIcon />
            </Button>
          )}
          <Input
            ref={inputRef}
            defaultValue={originalAmount}
            align="center"
            onKeyDown={handleChange}
            onBlur={handleUpdateValue}
            error={error}
            type="number"
            min="0"
            onFocus={() => setShowIncrementInfo(minIncrement > 1 && true)}
            step={minIncrement}
            size={size}
            className={styles.input}
          />
          {!hideButtons && (
            <Button
              type="button"
              variant="unstyled"
              className={`${styles.control} ${styles.plus}`}
              onClick={handleControlClick(minIncrement)}
            >
              <PlusIcon />
            </Button>
          )}
        </Group>
      </Popover.Target>

      <Popover.Dropdown>
        This product requires increments of {minIncrement}
      </Popover.Dropdown>
    </Popover>
  );
};
