import { useMemo } from 'react';
import { CartItemType } from '@/libs/cart/types';
import {
  getBuyXGetYPromotionDetails,
  groupTriggeredPromotionItems,
  hasTriggeredPromotion,
  getPromotionData,
} from '../utils/promotionUtils';
import { BuyXGetYPromotionData, ProcessedPromotionData } from '../types';

export function usePromotionData(
  items: CartItemType[],
): ProcessedPromotionData {
  return useMemo(() => {
    let promotions: Record<string, { items: CartItemType[] }> & {
      buy_x_get_y?: BuyXGetYPromotionData;
    } = groupTriggeredPromotionItems(items);

    if (promotions.buy_x_get_y) {
      promotions = {
        ...promotions,
        buy_x_get_y: {
          ...getBuyXGetYPromotionDetails(promotions.buy_x_get_y.items),
          items: promotions.buy_x_get_y.items.map(getPromotionData),
        },
      };
    }

    // Filter items that don't have promotions or don't have triggered promotions
    const nonPromotionItems = items.filter(
      (item) => !hasTriggeredPromotion(item),
    );

    return {
      promotions,
      nonPromotionItems,
    };
  }, [items]);
}
