{"name": "web-ui-pure", "version": "0.1.0", "private": true, "type": "module", "scripts": {"start": "vite", "start:gpo": "APP=gpo-portal vite", "build": "tsc & vite build", "build:shop": "APP=shop npm run build", "build:gpo": "APP=gpo-portal npm run build", "build:all": "npm run build:shop && npm run build:gpo", "preview": "vite preview", "check-types": "tsc --noemit", "eslint": "eslint .", "lint": "npm run eslint && npm run check-types && npm run prettier", "lint-fix": "eslint --fix .", "prettier": "prettier . --check", "prettier-fix": "prettier . --write", "test": "vitest run", "test-ui": "vitest --ui", "coverage": "vitest run --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "simple-git-hooks": {"pre-commit": "npm run lint", "pre-push": "npm run test"}, "dependencies": {"@hookform/resolvers": "^3.3.4", "@mantine/core": "^7.9.2", "@mantine/dates": "^7.9.2", "@mantine/dropzone": "^7.9.2", "@mantine/hooks": "^7.9.2", "@mantine/notifications": "^7.9.2", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.17.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.10", "dompurify": "^3.2.5", "i18next": "^23.16.4", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "marked": "^15.0.8", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-csv": "^2.2.2", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.51.4", "react-i18next": "^14.1.1", "react-loading-skeleton": "^3.5.0", "react-router-dom": "^6.22.3", "recharts": "^2.15.1", "tailwind-merge": "^3.3.1", "yup": "^1.4.0", "zustand": "^4.5.2"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.4", "@storybook/addon-essentials": "^8.5.6", "@storybook/addon-interactions": "^8.5.6", "@storybook/addon-onboarding": "^8.5.6", "@storybook/addon-styling-webpack": "^1.0.1", "@storybook/blocks": "^8.5.6", "@storybook/react": "^8.5.6", "@storybook/react-vite": "^8.5.6", "@storybook/test": "^8.5.6", "@tailwindcss/postcss": "^4.1.11", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.5.2", "@types/node": "^18.19.33", "@types/react": "^18.2.79", "@types/react-csv": "^1.1.10", "@types/react-dom": "^18.2.25", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.24.1", "@vitejs/plugin-react": "^4.3.3", "@vitest/coverage-v8": "^2.0.5", "@vitest/ui": "^2.0.5", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "^0.11.2", "jsdom": "^24.0.0", "postcss": "^8.4.38", "postcss-import": "^16.1.0", "postcss-nesting": "^12.1.1", "postcss-preset-mantine": "^1.14.4", "postcss-simple-vars": "^7.0.1", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "prettier-plugin-tailwindcss": "^0.6.13", "simple-git-hooks": "^2.9.0", "storybook": "^8.5.6", "storybook-dark-mode": "^4.0.2", "tailwindcss": "^4.1.11", "typescript": "^5.6.3", "vite": "5.4.6", "vite-plugin-pwa": "^1.0.2", "vite-plugin-svgr": "^4.2.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.0.5"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}